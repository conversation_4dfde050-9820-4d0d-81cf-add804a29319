    /*
    Flaticon icon font: Flaticon
    Creation date: 11/02/2019 11:47
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-mexican-taco:before { content: "\f100"; }
.flaticon-house:before { content: "\f101"; }
.flaticon-guitar:before { content: "\f102"; }
.flaticon-gym:before { content: "\f103"; }
.flaticon-shopping-bag:before { content: "\f104"; }
.flaticon-cocktail:before { content: "\f105"; }
    
    $font-Flaticon-mexican-taco: "\f100";
    $font-Flaticon-house: "\f101";
    $font-Flaticon-guitar: "\f102";
    $font-Flaticon-gym: "\f103";
    $font-Flaticon-shopping-bag: "\f104";
    $font-Flaticon-cocktail: "\f105";